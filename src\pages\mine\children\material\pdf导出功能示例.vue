<template>
  <view>
    <uni-search-bar v-show="isTeacher" @confirm="onSearchConfirm" @cancel="cancel" @input="onSearchInput" :focus="true" v-model="userName" placeholder="请输入姓名" />
    <view v-if="showSuggestions" class="suggestion-container">
      <view v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item" @click="selectSuggestion(suggestion)">
        {{ suggestion.userName }}
      </view>
    </view>
    <!-- <uni-fab :popMenu="false" ref="fab" class="downloadBtn" type="primary"  @click="render2.myprint" /> -->
    <button
      class="downloadBtn"
      type="primary"
      @click="render2.myprint"
      style="box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px; z-index: 9999; position: fixed; top: 25vh; font-size: 22px; left: 3vw; line-height: 50px; width: 50px; height: 50px; border-radius: 50%"
    >
      ＋
    </button>

    <ly-curriculumtable
      :weekTableState="true"
      :weekTableNumber="weekNumbers"
      :controlWeek="controlWeek"
      id="pdfDom"
      :timetableType="timetableType"
      :timetables="timetables"
      :startdDate="startdDate"
      :weekButton="weekButton"
      @nextWeekClick="nextWeekClick"
      @lastWeekClick="lastWeekClick"
      @courseClick="courseClick"
      @weekSelectClick="weekSelectClick"
      @weekOpenClick="weekOpenClick"
    >
    </ly-curriculumtable>
    <!-- 课程详情 -->
    <u-modal class="modal" :show="showMyModal" title="课程详情" :closeOnClickOverlay="true" :showConfirmButton="false" @close="closeModal">
      <view class="slot-content">
        <u-cell-group>
          <u-cell v-for="(item, index) in modalItem" :key="index" :title="item" :icon="modalIcon[index]" :iconStyle="{ color: colorList()[index + 1] }" size="large"></u-cell>
        </u-cell-group>
      </view>
    </u-modal>
  </view>
</template>
<script lang="renderjs" module="render2">
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
export default {
	data() {
		return {}
	},
	mounted() {},
	methods: {
		myprint(data) {
			this.$ownerInstance.callMethod('showLoading');
			const detail = document.querySelector("#pdfDom");


			html2Canvas(detail, {
				allowTaint: true,
				useCORS: true,
				scale: 2, // 提高分辨率
				windowHeight: detail.scrollHeight // 确保捕获完整高度
			}).then((canvas) => {
				return new Promise((resolve) => {
					setTimeout(() => resolve(canvas), 500)
				}).then((canvas) => {
					const contentWidth = canvas.width;
					const contentHeight = canvas.height;

					// 创建自定义尺寸的PDF（核心修改）
					const pdf = new JsPDF({
						orientation: contentWidth > contentHeight ? 'l' : 'p', // 自动方向
						unit: 'px',
						format: [contentWidth, contentHeight] // 完全匹配内容尺寸
					});

					// 直接添加完整内容（移除分页逻辑）
					pdf.addImage(canvas, 'PNG', 0, 0, contentWidth, contentHeight);

					const blob = pdf.output("datauristring");
					this.$ownerInstance.callMethod('downPdf', blob);
				}).catch((r) => {
					console.log(r);
					this.$ownerInstance.callMethod('hideLoading');
				})
			});

		}

	},
}
</script>
<script>
import request from '@/utils/request'
import { mapMutations } from 'vuex'
import { checkRole } from '@/utils/permission.js'
import { toast, showConfirm, tansParams } from '@/utils/common'
import user from '../../store/modules/user'
import constant from '../../utils/constant'
import { getUserList } from '@/api/system/user.js'
import { getToken } from '@/utils/auth'
import storage from '../../utils/storage'
export default {
  data() {
    return {
      htmlTitle: '学习计划导出',
      content: [
        {
          iconPath: '/static/images/upload.png',
          selectedIconPath: '/static/images/upload.png',
          text: '导出',
          active: false
        }
      ],
      userName: '',
      isTeacher: false,
      weekNumbers: 20, //一共显示几周
      controlWeek: 1, //显示的第几周
      weekButton: true, //开启上一周下一周按钮
      startdDate: '', //开始时间  默认为当前时间
      nowDay: '', //几号
      nowMonth: '', //几月
      nowYear: '', //几年
      showSuggestions: false, // 控制建议列表的显示
      suggestions: [], // 存储姓名建议
      debounceTimer: null, // 用于防抖的定时器
      isTeacher: false,
      timetables: [],
      timetableType: [
        {
          name: '07:30至08:30'
        },
        {
          name: '09:00至12:00'
        },
        {
          name: '14:00至17:00'
        },
        {
          name: '19:00至22:00'
        }
      ],
      //弹窗属性
      showMyModal: false, //课程详情弹窗
      modalIcon: ['calendar', 'home', 'server-man', 'clock'], //图标
      modalItem: [] //依次是课程名、教室、教师、上课时间--为了适配渲染
    }
  },
  created() {
    const { year, month, day } = this.getCurrentWeekMondayYMD()
    console.log('当前时间', uni.getStorageSync('userInfo'))
    this.nowDay = day
    this.nowMonth = month
    this.nowYear = year
  },
  mounted() {
    // 判断用户角色是admin或teacher时设置isTeacher为true
    // const userRoles = storage.get(constant.roles) || []
    // this.isTeacher = userRoles.includes('admin') || userRoles.includes('teacher')
    this.isTeacher = checkRole(['管理员', '教师'])
    if (!this.isTeacher) {
      this.fetchData()
    }
  },
  methods: {
    base64ToFile(base64Str, fileName, callback) {
      //申请本地存储读写权限，创建文件夹
      plus.android.requestPermissions(
        ['android.permission.WRITE_EXTERNAL_STORAGE', 'android.permission.READ_EXTERNAL_STORAGE', 'android.permission.INTERNET', 'android.permission.ACCESS_WIFI_STATE'],
        (error) => {
          const File = plus.android.importClass('java.io.File')
          let file = new File('/storage/emulated/0/PDF存放处')
          if (!file.exists()) {
            //文件夹不存在即创建
            return file.mkdirs()
          }
          return false
        },
        (success) => {
          uni.showToast({
            title: '无法获取权限，文件下载将出错！',
            icon: 'none'
          })
        }
      )
      // 去除base64前缀,进行文件保存
      var index = base64Str.indexOf(',')
      var base64Str = base64Str.slice(index + 1, base64Str.length)
      let that = this
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function (fs) {
        fs.root.getFile(
          fileName,
          {
            create: true
          },
          function (entry) {
            // 获得本地路径URL，file:///xxx/doc/1663062980631.xlsx
            var fullPath = '/storage/emulated/0/PDF存放处/' + fileName
            var Base64 = plus.android.importClass('android.util.Base64')
            var FileOutputStream = plus.android.importClass('java.io.FileOutputStream')
            var out = new FileOutputStream(fullPath)
            // 此处Base64.decode有长度限制，如果不能满足需求，可以考虑换成官方原生插件市场的【Base64转文件】
            // var bytes = Base64.decode(base64Str, Base64.DEFAULT);
            //转成Array避免长度限制
            let bytes = that.base64ToByteArray(base64Str)
            out.write(bytes)
            out.close()
            // 回调
            callback && callback()
          }
        )
      })
    },
    downPdf(path) {
      var fileName = new Date().valueOf() + '.pdf'
      let that = this
      this.base64ToFile(path, fileName, function (path1) {
        that.hideLoading()
        uni.showToast({
          title: `已保存在文件夹下！位置为：/storage/emulated/0/PDF存放处/${fileName}`,
          icon: 'none',
          duration: 2000,
          position: 'top'
        })
        setTimeout(() => {
          //自行控制是否打开文件

          //用第三方程序打开文件
          plus.runtime.openFile(`/storage/emulated/0/PDF存放处/${fileName}`, {}, function (error) {
            plus.nativeUI.toast(error.message)
          })
        }, 2000)
      })
    },
    base64ToByteArray(base64Str) {
      const binaryString = atob(base64Str)
      const uint8Array = new Uint8Array(binaryString.length)

      for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i)
      }
      let arr = []
      Array.from(uint8Array).map((num) => {
        arr.push(num >= 128 ? num - 256 : num)
      })
      return arr
    },
    showLoading() {
      uni.showLoading({
        title: '正在导出中'
      })
    },
    hideLoading() {
      uni.hideLoading()
    },

    fabClick() {},
    switchBtn(hor, ver) {
      if (hor === 0) {
        this.direction = this.direction === 'horizontal' ? 'vertical' : 'horizontal'
        this.directionStr = this.direction === 'horizontal' ? '垂直' : '水平'
      } else {
        this.horizontal = hor
        this.vertical = ver
      }
      this.$forceUpdate()
    },
    switchColor() {
      this.is_color_type = !this.is_color_type
      if (this.is_color_type) {
        this.pattern.iconColor = '#aaa'
        this.pattern.buttonColor = '#fff'
      } else {
        this.pattern.iconColor = '#fff'
        this.pattern.buttonColor = '#007AFF'
      }
    },

    // 选择一个姓名建议
    selectSuggestion(suggestion) {
      console.log(suggestion)
      this.userName = suggestion.userName
      this.showSuggestions = false
      this.fetchData()
    },

    // 处理搜索框输入事件，用于获取姓名建议
    onSearchInput(e) {
      console.log('搜索输入:', e)
      // 清除之前的定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }

      // 设置新的定时器，实现防抖
      this.debounceTimer = setTimeout(() => {
        const value = e // 直接使用传入的值而不是this.userName
        if (value && value.length > 0) {
          this.fetchUserNameSuggestions(value)
        } else {
          this.suggestions = []
          this.showSuggestions = false
        }
      }, 300) // 300ms的防抖延迟
    },
    onSearchConfirm(res) {
      this.userName = res.value
      this.showSuggestions = false
      console.log('执行搜索:', res.value)
      this.fetchData()
    },
    cancel() {
      this.userName = ''
      this.suggestions = []
      this.showSuggestions = false
      this.fetchData()
    },
    transformSchedule(input) {
      // 定义时间段对应的列索引
      const timeSlotMap = {
        '7:30-8:30': 0,
        '9:00-12:00': 1,
        '14:00-17:00': 2,
        '19:00-22:00': 3
      }

      // 创建7行4列的二维数组，初始值为空字符串
      const result = Array.from(
        {
          length: 7
        },
        () => Array(4).fill('')
      )
      console.log(input, 'input')

      // 遍历每一天的数据
      input.forEach((dayData, dayIndex) => {
        // console.log(dayData,'dayData')
        // 遍历当天的时间段数据
        dayData.forEach((item) => {
          console.log(item)
          const colIndex = timeSlotMap[item.timeSlot]
          if (colIndex !== undefined) {
            result[dayIndex][colIndex] = item.learnContent
          }
        })
      })
      console.log('result', result)

      return result
    },
    // 请求姓名建议列表
    async fetchUserNameSuggestions(name) {
      console.log('开始获取建议, 输入值:', name)
      if (!name || name.trim() === '') {
        this.suggestions = []
        this.showSuggestions = false
        return
      }
      //代码优化
      getUserList(name, 10)
        .then((response) => {
          console.log('姓名建议:', response)
          if (response && Array.isArray(response)) {
            this.suggestions = response
            this.showSuggestions = this.suggestions.length > 0
          } else {
            this.suggestions = []
            this.showSuggestions = false
          }
        })
        .catch((error) => {
          console.error('获取姓名建议失败:', error)
          this.suggestions = []
          this.showSuggestions = false
        })
    },

    // 选择一个姓名建议
    selectSuggestion(suggestion) {
      console.log(suggestion)
      this.userName = suggestion.userName
      this.userId = suggestion.id
      this.showSuggestions = false
      this.fetchData()
    },
    getCurrentWeekMondayYMD() {
      const date = new Date()
      const dayOfWeek = date.getDay()
      const diff = dayOfWeek === 0 ? 6 : dayOfWeek - 1
      date.setDate(date.getDate() - diff)

      return {
        year: date.getFullYear(), // 年（四位数）
        month: date.getMonth() + 1, // 月（1-12）
        day: date.getDate() // 日（1-31）
      }
    },
    courseClick(re) {
      console.log(re)

      //console.log(JSON.stringify(re))
      this.modalItem[0] = '课程: ' + re.name
      this.modalItem[1] = '教室: 213'
      this.modalItem[2] = '教师: 吴老师'
      this.modalItem[3] = '时间: 9:00-9:40'
      this.showMyModal = true
      //console.log(",",JSON.stringify(this.modalItem))
    },
    //转化年月日
    formatDate(year, month, day) {
      // 创建Date对象（注意月份要减1）
      const date = new Date(year, month - 1, day)

      // 获取年月日（月份需要加1恢复为自然月）
      const y = date.getFullYear()
      const m = date.getMonth() + 1 // 月份从0开始，转成自然月1-12
      const d = date.getDate()

      // 拼接成目标格式（时间部分固定补零）
      return `${y}/${m}/${d} 00:00:00`
    },
    // 请求后端 API 获取打卡数据
    async fetchData() {
      try {
        //
        let id = ''
        await this.$store.dispatch('GetInfo').then((res) => {
          console.log('获取用户信息结果', res.id)

          id = res.id
        })

        if (this.userId) {
          console.log('获取用户信息结果', this.userId)
          id = this.userId
        }
        console.log('id:' + id)
        uni.showLoading({
          title: '加载中'
        })
        const response = await request({
          url: '/ApiCaPlan/GetList',
          Headers: {
            'Access-Control-Allow-Methods': 'POST, GET, OPTIONS'
          },
          method: 'POST',
          data: {
            beginTime: this.formatDate(this.nowYear, this.nowMonth, this.nowDay),
            userId: id
          }
        })
        if (response.length > 0) {
          console.log(response)
          this.timetables = this.transformSchedule(
            response.map((i) => {
              console.log(i.planJson)
              return JSON.parse(i.planJson)
            })
          )
        } else {
          uni.showToast({
            title: '暂无数据',
            icon: 'error'
          })
          this.timetables = []
        }
        uni.hideLoading()
        console.log('response', response)
      } catch (error) {
        console.error('请求失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '请求失败',
          icon: 'none'
        })
      }
    },
    nextWeekClick(e, d) {
      console.log(d)
      if (this.nowDay > d[0]) {
        this.nowMonth == 12 ? ((this.nowMonth = 1), (this.nowYear += 1)) : (this.nowMonth += 1)
      }
      this.nowDay = d[0]
      console.log(this.nowDay, this.nowMonth, this.nowYear)
      this.fetchData()
      // console.log("下一周", e, d)
    },
    lastWeekClick(e, d) {
      if (this.nowDay < d[0]) {
        this.nowMonth == 1 ? ((this.nowMonth = 12), (this.nowYear -= 1)) : (this.nowMonth -= 1)
      }
      this.nowDay = d[0]
      console.log(this.nowDay, this.nowMonth, this.nowYear)
      this.fetchData()
      // if()
      // console.log("上一周", e, d)
    },
    weekOpenClick() {
      console.log('点击了第几周')
    },
    weekSelectClick(e) {
      console.log('您选择了', e)
    },
    //关闭弹窗
    closeModal() {
      this.showMyModal = this.showMyModal == true ? false : true
    },
    //返回颜色
    colorList() {
      return ['#85B0FD', '#FFC44F', '#AB7FF8', '#FEA17C', '#FF7F4F', '#7FCFF8', '#AEEC34']
    }
  }
}
</script>
<style scoped>
/* 姓名建议列表样式 */
.suggestion-container {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 14px;
  margin: 0 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
  max-height: 200px;
  overflow-y: auto;
  z-index: 100;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transform: translateY(5px);
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestion-item {
  padding: 14px 18px;
  border-bottom: 1px solid rgba(240, 240, 240, 0.8);
  font-size: 14px;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background-color: rgba(245, 247, 250, 0.8);
}

.suggestion-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background: linear-gradient(90deg, #6a89cc, #4a69bd);
  z-index: -1;
  transition: height 0.25s ease;
  opacity: 0;
}

.suggestion-item:hover {
  color: #4a69bd;
  padding-left: 24px;
}

.suggestion-item:hover::after {
  height: 3px;
  opacity: 1;
}
</style>
