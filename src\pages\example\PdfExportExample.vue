<template>
  <view class="container">
    <view class="header">
      <text class="title">PDF导出功能示例</text>
    </view>

    <!-- PDF导出组件 -->
    <PdfExport
      export-element-id="content-to-export"
      :file-name="pdfFileName"
      :auto-open="true"
      save-path="/storage/emulated/0/PDF存放处"
      @export-complete="onExportComplete"
      @export-error="onExportError"
      @pdf-generated="onPdfGenerated"
    >
      <!-- 要导出的内容 -->
      <view id="content-to-export" class="export-content">
        <view class="content-header">
          <text class="content-title">{{ reportTitle }}</text>
          <text class="content-date">生成时间：{{ currentDate }}</text>
        </view>

        <view class="content-body">
          <view class="section">
            <text class="section-title">基本信息</text>
            <view class="info-item">
              <text class="label">姓名：</text>
              <text class="value">{{ userInfo.name }}</text>
            </view>
            <view class="info-item">
              <text class="label">部门：</text>
              <text class="value">{{ userInfo.department }}</text>
            </view>
            <view class="info-item">
              <text class="label">职位：</text>
              <text class="value">{{ userInfo.position }}</text>
            </view>
          </view>

          <view class="section">
            <text class="section-title">数据统计</text>
            <view class="chart-container">
              <!-- 这里可以放置图表组件 -->
              <view class="mock-chart">
                <text>图表区域</text>
              </view>
            </view>
          </view>

          <view class="section">
            <text class="section-title">详细列表</text>
            <view class="table-container">
              <view class="table-header">
                <text class="table-cell">序号</text>
                <text class="table-cell">项目名称</text>
                <text class="table-cell">状态</text>
                <text class="table-cell">完成度</text>
              </view>
              <view 
                v-for="(item, index) in tableData" 
                :key="index" 
                class="table-row"
              >
                <text class="table-cell">{{ index + 1 }}</text>
                <text class="table-cell">{{ item.name }}</text>
                <text class="table-cell">{{ item.status }}</text>
                <text class="table-cell">{{ item.progress }}%</text>
              </view>
            </view>
          </view>
        </view>

        <view class="content-footer">
          <text class="footer-text">报告生成完毕</text>
        </view>
      </view>
    </PdfExport>

    <!-- 其他操作按钮 -->
    <view class="actions">
      <button @click="refreshData" class="action-btn">刷新数据</button>
      <button @click="previewContent" class="action-btn">预览内容</button>
    </view>
  </view>
</template>

<script>
import PdfExport from '@/components/PdfExport.vue'

export default {
  name: 'PdfExportExample',
  components: {
    PdfExport
  },
  data() {
    return {
      reportTitle: '工作报告',
      currentDate: '',
      userInfo: {
        name: '张三',
        department: '技术部',
        position: '前端工程师'
      },
      tableData: [
        { name: '项目A', status: '进行中', progress: 75 },
        { name: '项目B', status: '已完成', progress: 100 },
        { name: '项目C', status: '待开始', progress: 0 },
        { name: '项目D', status: '进行中', progress: 45 }
      ]
    }
  },
  computed: {
    pdfFileName() {
      return `${this.reportTitle}_${new Date().valueOf()}.pdf`
    }
  },
  mounted() {
    this.currentDate = this.formatDate(new Date())
  },
  methods: {
    /**
     * 格式化日期
     */
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    /**
     * 刷新数据
     */
    refreshData() {
      this.currentDate = this.formatDate(new Date())
      uni.showToast({
        title: '数据已刷新',
        icon: 'success'
      })
    },

    /**
     * 预览内容
     */
    previewContent() {
      uni.showModal({
        title: '预览',
        content: '这是要导出的内容预览',
        showCancel: false
      })
    },

    /**
     * 导出完成回调
     */
    onExportComplete() {
      console.log('PDF导出完成')
      uni.showToast({
        title: 'PDF导出成功',
        icon: 'success'
      })
    },

    /**
     * 导出错误回调
     */
    onExportError(error) {
      console.error('PDF导出失败:', error)
      uni.showModal({
        title: '导出失败',
        content: error || '未知错误',
        showCancel: false
      })
    },

    /**
     * PDF生成回调（小程序使用）
     */
    onPdfGenerated(pdfData) {
      console.log('PDF数据生成完成:', pdfData.length)
      // 在小程序中可以将PDF数据上传到服务器
      // this.uploadPdfToServer(pdfData)
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.export-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.content-header {
  border-bottom: 2px solid #007aff;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.content-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.content-date {
  font-size: 14px;
  color: #666;
}

.section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
  padding-left: 10px;
  border-left: 4px solid #007aff;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.label {
  width: 80px;
  color: #666;
  font-weight: bold;
}

.value {
  color: #333;
}

.chart-container {
  height: 200px;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mock-chart {
  color: #999;
  font-size: 16px;
}

.table-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  font-weight: bold;
}

.table-row {
  display: flex;
  border-top: 1px solid #eee;
}

.table-cell {
  flex: 1;
  padding: 10px;
  text-align: center;
  border-right: 1px solid #eee;
}

.table-cell:last-child {
  border-right: none;
}

.content-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.footer-text {
  color: #666;
  font-style: italic;
}

.actions {
  display: flex;
  justify-content: space-around;
}

.action-btn {
  flex: 1;
  margin: 0 10px;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
}
</style>
