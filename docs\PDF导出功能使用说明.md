# PDF导出功能使用说明

## 概述

基于 uni-app 的 renderjs 技术，结合 jspdf 和 html2canvas 库实现的跨平台 PDF 导出功能。支持 APP、H5 和小程序平台。

## 功能特点

- ✅ 跨平台支持（APP、H5、小程序）
- ✅ 高质量PDF输出（可配置分辨率）
- ✅ 自动适应内容尺寸
- ✅ 支持复杂页面布局
- ✅ 完整的错误处理
- ✅ 进度提示和用户反馈

## 依赖库

```bash
npm install jspdf html2canvas
```

## 核心文件

### 1. PdfExport.vue 组件

主要的PDF导出组件，包含：
- renderjs 模块：负责在视图层处理 DOM 和生成 PDF
- 逻辑层：处理文件保存、权限申请等平台相关功能

### 2. 使用示例

```vue
<template>
  <PdfExport
    export-element-id="content-to-export"
    :file-name="pdfFileName"
    :auto-open="true"
    save-path="/storage/emulated/0/PDF存放处"
    @export-complete="onExportComplete"
    @export-error="onExportError"
  >
    <view id="content-to-export">
      <!-- 要导出的内容 -->
    </view>
  </PdfExport>
</template>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| exportElementId | String | 'pdf-export-content' | 要导出的元素ID |
| fileName | String | 自动生成 | PDF文件名 |
| autoOpen | Boolean | true | 是否自动打开生成的PDF |
| savePath | String | '/storage/emulated/0/PDF存放处' | 保存路径（APP） |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| export-complete | - | 导出完成 |
| export-error | error | 导出失败 |
| pdf-generated | pdfData | PDF数据生成完成（小程序） |

## 平台差异

### APP 平台
- 支持本地文件保存
- 需要存储权限
- 可自动打开PDF文件

### H5 平台
- 通过浏览器下载
- 无需权限申请
- 用户手动保存

### 小程序平台
- 通过事件回调获取PDF数据
- 需要上传到服务器或其他处理方式
- 受平台限制较多

## 核心实现原理

### 1. renderjs 模块

```javascript
// 在 renderjs 中处理 DOM 和生成 PDF
export default {
  methods: {
    exportToPdf(options = {}) {
      const targetElement = document.querySelector(`#${elementId}`);
      
      html2Canvas(targetElement, canvasOptions).then((canvas) => {
        const pdf = new JsPDF(pdfOptions);
        pdf.addImage(canvas, 'PNG', 0, 0, contentWidth, contentHeight);
        const pdfData = pdf.output("datauristring");
        
        // 通知主线程处理
        this.$ownerInstance.callMethod('handlePdfData', pdfData);
      });
    }
  }
}
```

### 2. 文件保存（APP）

```javascript
// Base64 转文件保存
base64ToFile(base64Str, fileName, callback) {
  // 申请权限
  plus.android.requestPermissions([...], () => {
    // 创建目录
    const File = plus.android.importClass('java.io.File');
    const file = new File(this.savePath);
    if (!file.exists()) {
      file.mkdirs();
    }
    
    // 保存文件
    this.saveBase64File(base64Str, fileName, callback);
  });
}
```

## 使用步骤

### 1. 安装依赖

```bash
npm install jspdf html2canvas
```

### 2. 引入组件

```javascript
import PdfExport from '@/components/PdfExport.vue'

export default {
  components: {
    PdfExport
  }
}
```

### 3. 使用组件

```vue
<template>
  <PdfExport export-element-id="my-content">
    <view id="my-content">
      <!-- 要导出的内容 -->
    </view>
  </PdfExport>
</template>
```

## 配置选项

### html2canvas 配置

```javascript
const canvasOptions = {
  allowTaint: true,
  useCORS: true,
  scale: 2, // 分辨率倍数
  backgroundColor: '#ffffff',
  windowHeight: element.scrollHeight
};
```

### jsPDF 配置

```javascript
const pdfOptions = {
  orientation: 'p', // 'p' 竖向, 'l' 横向
  unit: 'px',
  format: [width, height] // 自定义尺寸
};
```

## 注意事项

1. **权限处理**：APP 平台需要申请存储权限
2. **内容尺寸**：确保导出内容有明确的宽高
3. **图片跨域**：注意图片的跨域问题，设置 useCORS: true
4. **性能优化**：大内容导出时注意内存使用
5. **样式兼容**：某些 CSS 样式可能在 canvas 中显示异常

## 常见问题

### Q: 导出的PDF是空白的？
A: 检查目标元素是否存在，ID是否正确，内容是否已渲染完成。

### Q: 图片不显示？
A: 设置 `useCORS: true` 和 `allowTaint: true`，确保图片支持跨域。

### Q: APP中无法保存文件？
A: 检查是否已申请存储权限，目录是否有写入权限。

### Q: 小程序中如何使用？
A: 监听 `pdf-generated` 事件，获取PDF数据后上传到服务器。

## 扩展功能

可以根据需要扩展以下功能：
- 多页PDF支持
- 水印添加
- 页眉页脚
- 自定义页面大小
- 批量导出
- 导出进度显示
