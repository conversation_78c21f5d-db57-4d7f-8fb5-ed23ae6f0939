<template>
  <view>
    <!-- PDF导出按钮 -->
    <button
      class="pdf-export-btn"
      type="primary"
      @click="render.exportToPdf"
      :disabled="isExporting"
    >
      {{ isExporting ? '导出中...' : '导出PDF' }}
    </button>

    <!-- 要导出的内容区域 -->
    <view :id="exportElementId" class="export-content">
      <slot></slot>
    </view>
  </view>
</template>

<script lang="renderjs" module="render">
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'

export default {
  data() {
    return {}
  },
  mounted() {},
  methods: {
    /**
     * 导出PDF功能
     * @param {Object} options - 导出配置选项
     */
    exportToPdf(options = {}) {
      // 通知主线程开始导出
      this.$ownerInstance.callMethod('startExport');
      
      // 获取要导出的DOM元素
      const elementId = options.elementId || this.$ownerInstance.callMethod('getExportElementId');
      const targetElement = document.querySelector(`#${elementId}`);
      
      if (!targetElement) {
        console.error('未找到要导出的元素');
        this.$ownerInstance.callMethod('exportError', '未找到要导出的元素');
        return;
      }

      // html2canvas配置
      const canvasOptions = {
        allowTaint: true,
        useCORS: true,
        scale: options.scale || 2, // 提高分辨率
        windowHeight: targetElement.scrollHeight, // 确保捕获完整高度
        backgroundColor: options.backgroundColor || '#ffffff',
        ...options.canvasOptions
      };

      html2Canvas(targetElement, canvasOptions).then((canvas) => {
        return new Promise((resolve) => {
          // 添加延迟确保渲染完成
          setTimeout(() => resolve(canvas), 500);
        }).then((canvas) => {
          const contentWidth = canvas.width;
          const contentHeight = canvas.height;

          // PDF配置
          const pdfOptions = {
            orientation: contentWidth > contentHeight ? 'l' : 'p', // 自动方向
            unit: 'px',
            format: [contentWidth, contentHeight], // 完全匹配内容尺寸
            ...options.pdfOptions
          };

          // 创建PDF
          const pdf = new JsPDF(pdfOptions);
          
          // 添加图片到PDF
          pdf.addImage(canvas, 'PNG', 0, 0, contentWidth, contentHeight);

          // 获取PDF的base64数据
          const pdfData = pdf.output("datauristring");
          
          // 通知主线程处理PDF数据
          this.$ownerInstance.callMethod('handlePdfData', pdfData);
        });
      }).catch((error) => {
        console.error('PDF导出失败:', error);
        this.$ownerInstance.callMethod('exportError', error.message);
      });
    }
  }
}
</script>

<script>
export default {
  name: 'PdfExport',
  props: {
    // 要导出的元素ID
    exportElementId: {
      type: String,
      default: 'pdf-export-content'
    },
    // 导出文件名
    fileName: {
      type: String,
      default: () => `export_${new Date().valueOf()}.pdf`
    },
    // 是否自动打开文件
    autoOpen: {
      type: Boolean,
      default: true
    },
    // 保存路径
    savePath: {
      type: String,
      default: '/storage/emulated/0/PDF存放处'
    }
  },
  data() {
    return {
      isExporting: false
    }
  },
  methods: {
    /**
     * 获取导出元素ID
     */
    getExportElementId() {
      return this.exportElementId;
    },

    /**
     * 开始导出
     */
    startExport() {
      this.isExporting = true;
      uni.showLoading({
        title: '正在导出PDF...'
      });
    },

    /**
     * 处理PDF数据
     */
    handlePdfData(pdfData) {
      // #ifdef APP-PLUS
      this.savePdfToLocal(pdfData);
      // #endif
      
      // #ifdef H5
      this.downloadPdfInBrowser(pdfData);
      // #endif
      
      // #ifdef MP
      this.handleMiniProgramPdf(pdfData);
      // #endif
    },

    /**
     * 保存PDF到本地 (APP)
     */
    savePdfToLocal(pdfData) {
      const fileName = this.fileName;
      this.base64ToFile(pdfData, fileName, () => {
        this.exportComplete();
        uni.showToast({
          title: `已保存到：${this.savePath}/${fileName}`,
          icon: 'none',
          duration: 3000
        });

        if (this.autoOpen) {
          setTimeout(() => {
            plus.runtime.openFile(`${this.savePath}/${fileName}`, {}, (error) => {
              console.error('打开文件失败:', error);
            });
          }, 1000);
        }
      });
    },

    /**
     * 浏览器下载PDF (H5)
     */
    downloadPdfInBrowser(pdfData) {
      const link = document.createElement('a');
      link.href = pdfData;
      link.download = this.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      this.exportComplete();
    },

    /**
     * 小程序处理PDF
     */
    handleMiniProgramPdf(pdfData) {
      // 小程序中可以将PDF数据上传到服务器或使用其他方式处理
      this.$emit('pdf-generated', pdfData);
      this.exportComplete();
    },

    /**
     * Base64转文件
     */
    base64ToFile(base64Str, fileName, callback) {
      // 申请存储权限
      plus.android.requestPermissions(
        [
          'android.permission.WRITE_EXTERNAL_STORAGE',
          'android.permission.READ_EXTERNAL_STORAGE'
        ],
        () => {
          // 创建文件夹
          const File = plus.android.importClass('java.io.File');
          const file = new File(this.savePath);
          if (!file.exists()) {
            file.mkdirs();
          }

          // 保存文件
          this.saveBase64File(base64Str, fileName, callback);
        },
        () => {
          uni.showToast({
            title: '无法获取存储权限',
            icon: 'none'
          });
          this.exportError('无法获取存储权限');
        }
      );
    },

    /**
     * 保存Base64文件
     */
    saveBase64File(base64Str, fileName, callback) {
      // 去除base64前缀
      const index = base64Str.indexOf(',');
      const base64Data = base64Str.slice(index + 1);
      
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, (fs) => {
        fs.root.getFile(fileName, { create: true }, () => {
          const fullPath = `${this.savePath}/${fileName}`;
          const Base64 = plus.android.importClass('android.util.Base64');
          const FileOutputStream = plus.android.importClass('java.io.FileOutputStream');
          const out = new FileOutputStream(fullPath);
          
          // 转换为字节数组避免长度限制
          const bytes = this.base64ToByteArray(base64Data);
          out.write(bytes);
          out.close();
          
          callback && callback();
        });
      });
    },

    /**
     * Base64转字节数组
     */
    base64ToByteArray(base64Str) {
      const binaryString = atob(base64Str);
      const uint8Array = new Uint8Array(binaryString.length);

      for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
      }

      const arr = [];
      Array.from(uint8Array).forEach(num => {
        arr.push(num >= 128 ? num - 256 : num);
      });
      return arr;
    },

    /**
     * 导出完成
     */
    exportComplete() {
      this.isExporting = false;
      uni.hideLoading();
      this.$emit('export-complete');
    },

    /**
     * 导出错误
     */
    exportError(message) {
      this.isExporting = false;
      uni.hideLoading();
      uni.showToast({
        title: message || '导出失败',
        icon: 'none'
      });
      this.$emit('export-error', message);
    }
  }
}
</script>

<style scoped>
.pdf-export-btn {
  margin: 10px;
  border-radius: 8px;
  font-size: 16px;
  padding: 12px 24px;
}

.export-content {
  width: 100%;
}
</style>
